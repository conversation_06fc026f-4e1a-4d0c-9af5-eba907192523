{"mcpServers": {"browsermcp": {"command": "npx", "args": ["@browsermcp/mcp@latest"], "alwaysAllow": ["click_element", "browser_navigate", "browser", "browser_click", "browser_snapshot", "browser_go_forward", "browser_go_back", "browser_hover", "browser_type", "browser_select_option", "browser_press_key", "browser_wait", "browser_get_console_logs", "browser_screenshot"]}, "playwright-mcp": {"command": "npx", "args": ["playwright-mcp"], "alwaysAllow": ["page_goto", "page_fill", "page_click", "page_wait_for_selector", "page_wait_for_load_state", "execute-code", "init-browser", "get-context"]}}}