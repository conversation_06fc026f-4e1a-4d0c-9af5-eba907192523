{"version": "0.2.0", "configurations": [{"name": ".NET Core Launch (web)", "type": "coreclr", "request": "launch", "preLaunchTask": "build", "program": "${workspaceFolder}/LeaveBalanceAIAgent/bin/Debug/net8.0/LeaveBalanceAIAgent.dll", "args": [], "cwd": "${workspaceFolder}/LeaveBalanceAIAgent", "stopAtEntry": false, "serverReadyAction": {"action": "openExternally", "pattern": "\\bNow listening on:\\s+(https?://\\S+)"}, "env": {"ASPNETCORE_ENVIRONMENT": "Development"}, "sourceFileMap": {"/Views": "${workspaceFolder}/Views"}}, {"name": "Python: AI Agent", "type": "python", "request": "launch", "program": "${workspaceFolder}/AIAgent/agent.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}/AIAgent", "python": "${workspaceFolder}/AIAgent/venv/Scripts/python.exe"}]}