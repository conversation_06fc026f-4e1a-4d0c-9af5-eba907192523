{"python.defaultInterpreterPath": "./AIAgent/venv/Scripts/python.exe", "python.terminal.activateEnvironment": true, "python.linting.enabled": true, "python.linting.pylintEnabled": true, "python.formatting.provider": "black", "python.analysis.typeCheckingMode": "basic", "dotnet.defaultSolution": "TEST_AUGMENT_1.sln", "omnisharp.enableEditorConfigSupport": true, "omnisharp.enableRoslynAnalyzers": true, "files.exclude": {"**/bin": true, "**/obj": true, "**/.vs": true, "**/node_modules": true, "**/__pycache__": true, "**/*.pyc": true}, "files.associations": {"*.cshtml": "html", "*.razor": "html"}, "emmet.includeLanguages": {"razor": "html", "cshtml": "html"}, "dotnet.completion.showCompletionItemsFromUnimportedNamespaces": true}