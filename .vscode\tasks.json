{"version": "2.0.0", "tasks": [{"label": "build", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/LeaveBalanceAIAgent/LeaveBalanceAIAgent.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile"}, {"label": "publish", "command": "dotnet", "type": "process", "args": ["publish", "${workspaceFolder}/LeaveBalanceAIAgent/LeaveBalanceAIAgent.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile"}, {"label": "watch", "command": "dotnet", "type": "process", "args": ["watch", "run", "--project", "${workspaceFolder}/LeaveBalanceAIAgent/LeaveBalanceAIAgent.csproj"], "problemMatcher": "$msCompile"}, {"label": "restore", "command": "dotnet", "type": "process", "args": ["restore", "${workspaceFolder}/LeaveBalanceAIAgent/LeaveBalanceAIAgent.csproj"], "problemMatcher": "$msCompile"}, {"label": "test-ollama", "command": "${workspaceFolder}/AIAgent/venv/Scripts/python.exe", "type": "process", "args": ["${workspaceFolder}/AIAgent/test_ollama_connection.py"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}]}